import FeedbackUtil from '@/utils/feedback.js'
import HttpRequest from '@/utils/http.js'

export default {
  get (id, options) {
    return new HttpRequest().ajax('/llm/conversation/get', {
      id
    }, options)
  },
  // 我的会话
  me (options) {
    return new HttpRequest().ajax('/llm/conversation/me', null, options)
  },
  // 新建会话
  create (applicationId, originId, options) {
    return new HttpRequest().ajax('/llm/conversation/new', {
      applicationId,
      originId
    }, options)
  },
  // 重命名
  rename (id, title, options) {
    return new HttpRequest().ajax('/llm/conversation/rename', {
      id,
      title
    }, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/conversation/remove', {
      id
    }, options)
  },
  generate (id, model, plugins, memorySize, mediaId, message, onopen, onmessage, onclose, onerror) {
    return new HttpRequest().eventSource('/llm/conversation/chat', {
      id,
      model,
      plugins,
      memorySize,
      mediaId,
      message
    }, onopen, onmessage, onclose, onerror)
  },
  // 获取记忆
  memories (id, options) {
    return new HttpRequest().ajax('/llm/conversation/memory', {
      id
    }, options)
  },
  // 清理记忆
  clearMemory (id, options) {
    return new HttpRequest().ajax('/llm/conversation/memory/clear', {
      id
    }, options)
  },
  accept (options) {
    return new HttpRequest().ajax('/llm/conversation/document/accept', null, options)
  },
  findDocuments (id, options) {
    return new HttpRequest().ajax('/llm/conversation/document/find', {
      id
    }, options)
  },
  // 上传文件
  uploadDocument (id, bucket, files, options) {
    if (typeof bucket === 'string') {
      bucket = bucket.replaceAll(/\\/g, '/')
    }

    const _data = new FormData()

    files.forEach(i => {
      _data.append('files', i)
    })

    return new Promise((resolve, reject) => {
      new HttpRequest({
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Requested-With': ''
        }
      }).ajax(typeof bucket === 'string' ? `/llm/conversation/document/upload?conversationId=${id}&bucket=${bucket}` : `/llm/conversation/document/upload?conversationId=${id}`, _data, options)
        .then(result => {
          if (files.length === 1 && !result.data[0].success) {
            if (!options.toast || options.toast.error !== true) {
              FeedbackUtil.message(result.data[0].message, 'error')
            }

            // eslint-disable-next-line prefer-promise-reject-errors
            reject({
              message: result.data[0].message
            })
          } else {
            resolve(result)
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 删除文件
  removeDocument (id, options) {
    return new HttpRequest().ajax('/llm/conversation/document/remove', {
      id
    }, options)
  }
}
