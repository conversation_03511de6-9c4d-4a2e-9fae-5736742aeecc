import HttpRequest from '@/utils/http.js'

export default {
  get (id, options) {
    return new HttpRequest().ajax('/llm/application/get', {
      id
    }, options)
  },
  installId (difyId, options) {
    return new HttpRequest().ajax('/llm/application/get/install-id', {
      difyId
    }, options)
  },
  // 模糊搜索
  search (count, index, type, name, options) {
    return new HttpRequest().ajax('/llm/application/search/default', {
      count,
      index,
      type,
      name
    }, options)
  },
  searchUserApplications (type, name, options) {
    return new HttpRequest().ajax('/llm/application/search/scope', {
      type,
      name
    }, options)
  },
  me (options) {
    return new HttpRequest().ajax('/llm/application/me', null, options)
  },
  // 保存知识库应用
  saveDataset (record, options) {
    return new HttpRequest().ajax('/llm/application/save/dataset', record, options)
  },
  // 保存外链应用
  saveLink (record, options) {
    return new HttpRequest().ajax('/llm/application/save/link', record, options)
  },
  // 保存提示词应用
  savePrompt (record, options) {
    return new HttpRequest().ajax('/llm/application/save/prompt', record, options)
  },
  // 保存工作流应用
  saveWorkflow (record, options) {
    return new HttpRequest().ajax('/llm/application/save/workflow', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/application/remove', {
      id
    }, options)
  },
  addScope (id, type, targetId, options) {
    return new HttpRequest().ajax('/llm/application/scope/add', {
      applicationId: id,
      type,
      targetId
    }, options)
  },
  searchScopes (count, index, applicationId, name, options) {
    return new HttpRequest().ajax('/llm/application/scope/search', {
      count,
      index,
      applicationId,
      name
    }, options)
  },
  removeScope (scopeId, options) {
    return new HttpRequest().ajax('/llm/application/scope/remove', {
      scopeId
    }, options)
  }
}
