import '@/utils/date.js'
import HttpRequest from '@/utils/http.js'

export default {
  operation (options) {
    return new HttpRequest().ajax('/sys/stat/operation', null, options)
  },
  // 获取日活跃用户数
  dau (startDate, endDate, method, individual, options) {
    return new HttpRequest().ajax('/sys/stat/dau', {
      startDate: startDate ? new Date(startDate).format('yyyy-MM-dd') : null,
      endDate: endDate ? new Date(endDate).format('yyyy-MM-dd') : null,
      method,
      individual
    }, options)
  },
  // 获取日活跃率
  dar (startDate, endDate, method, options) {
    return new HttpRequest().ajax('/sys/stat/dar', {
      startDate: startDate ? new Date(startDate).format('yyyy-MM-dd') : null,
      endDate: endDate ? new Date(endDate).format('yyyy-MM-dd') : null,
      method
    }, options)
  },
  // 获取月留存率
  urr (field, options) {
    return new HttpRequest().ajax('/sys/stat/urr', {
      field
    }, options)
  },
  // 获取活跃分时数据
  trend (startDate, endDate, options) {
    return new HttpRequest().ajax('/sys/stat/trend', {
      startDate: startDate ? new Date(startDate).format('yyyy-MM-dd') : null,
      endDate: endDate ? new Date(endDate).format('yyyy-MM-dd') : null
    }, options)
  },
  // 获取最近最常访问方法
  mru (count, date, field, options) {
    return new HttpRequest().ajax('/sys/stat/mru', {
      count,
      date: date ? new Date(date).format('yyyy-MM-dd') : null,
      field
    }, options)
  }
}
