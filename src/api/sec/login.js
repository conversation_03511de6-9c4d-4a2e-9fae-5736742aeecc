import jquery from 'jquery'
import Store from '@/store/index.js'
import CryptoUtil from '@/utils/crypto.js'
import HttpRequest from '@/utils/http.js'

const appId = () => {
  return new HttpRequest().ajax('/util/cmpassport/app-id', null, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  })
}

const sign = str => {
  return new HttpRequest().ajax('/sec/login/cmpassport/sign', {
    str
  }, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  })
}

const passport = (token, userInformation, redirect, options) => {
  const _options = {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }

  jquery.extend(true, _options, options)

  return new HttpRequest().ajax('/sec/login/cmpassport', {
    token,
    userInformation,
    redirect
  }, _options)
}

export default {
  // 读取公钥
  publicKey (options) {
    return new HttpRequest().ajax('/sec/login/crypto/rsa/public-key', null, options)
  },
  // 生成图形验证码
  captcha (width, height, length, options) {
    const _data = {}

    if (Number.isFinite(width)) {
      _data.width = width
    }

    if (Number.isFinite(height)) {
      _data.height = height
    }

    if (Number.isFinite(length)) {
      _data.length = length
    }

    return new HttpRequest().ajax('/sec/login/captcha', _data, options)
  },
  // 发送短信验证码
  sendCode (mp, captchaText, captchaToken, options) {
    return new HttpRequest().ajax('/sec/login/sms-code', {
      mp,
      captcha: {
        text: captchaText,
        token: captchaToken
      }
    }, options)
  },

  // 账号密码登录
  loginByPassword (username, password, rememberMe, captchaText, captchaToken, options) {
    return this.publicKey({
      toast: {
        success: false
      }
    })
      .then(result => {
        return new HttpRequest().ajax('/sec/login/password', {
          username,
          password: CryptoUtil.encryptRsa(password, result.data),
          captcha: {
            text: captchaText,
            token: captchaToken
          },
          rememberMe
        }, options)
      })
      .catch(result => {
        return Promise.reject(result)
      })
  },
  // 短信验证码登录
  loginBySms (mp, smsCode, rememberMe, options) {
    return new HttpRequest().ajax('/sec/login/sms', {
      mp,
      smsCode,
      rememberMe
    }, options)
  },
  // 手机号码登录
  loginByCMPassport (redirect, options) {
    let _appId = null

    let _promise = appId().then(result => {
      _appId = result.data

      return {
        code: 'OK',
        data: YDRZ.getSign(_appId, '1.0')
      }
    })

    // 签名
    _promise = _promise.then(result => {
      return sign(result.data)
    })

    // 解密
    _promise = _promise.then(result => {
      return new Promise(resolve => {
        YDRZ.getTokenInfo({
          data: {
            appId: _appId,
            version: '1.0',
            sign: result.data,
            openType: '1', // 0为三网号码，1为移动号码
            expandParams: 'phoneNum=13509889129',
            isTest: '0'
          },
          success: (result) => {
            resolve(result)
          },
          error: (result) => {
            resolve(result)
          }
        })
      })
    })

    // 登录
    return new Promise(resolve => {
      _promise.then(result => {
        passport(result.data.token, result.data.userInformation, redirect, options).then(result => {
          resolve(result)
        })
      })
    })
  },
  // 企业号账号登录
  loginByWx (redirect, options) {
    return new HttpRequest().ajax('/sec/login/wx', {
      redirect
    }, options)
  },
  // 粤政易账号登录
  loginByYzy (redirect, options) {
    return new HttpRequest().ajax('/sec/login/yzy', {
      redirect
    }, options)
  },

  // 注销登录
  logout (options) {
    const _promise = new HttpRequest().ajax('/sec/login/cancel', null, options)

    // 清理数据
    _promise.then(() => {
      Store.commit('logout')
    })

    return _promise
  }
}
