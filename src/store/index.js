import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import { unreliablyUpdateRoutes } from '@/router/index.js'
import MediaApi from '@/api/media/record.js'

export default createStore({
  state () {
    return {
      // 全局锁
      lock: {
        // loading计数
        loading: 0
      },

      // 令牌
      credential: {
        // 是否已认证登录
        authenticated: false
      },

      // 数据资产
      assets: {
        // 用户
        user: null,

        // 权限
        permissions: null,

        // 页面
        pages: null
      }
    }
  },
  getters: {
    isLoading (state) {
      return state.lock.loading > 0
    }
  },
  mutations: {
    setUser (state, user) {
      if (user !== null) {
        state.credential.authenticated = true

        state.assets.user = {
          ...user,
          avatarUrl: typeof user.avatarId === 'string' ? MediaApi.preview(user.avatarId) : null
        }
      }
    },

    setPermissions (state, permissions) {
      if (Array.isArray(permissions)) {
        state.assets.permissions = [...permissions]
      }
    },

    setPages (state, pages) {
      if (!Array.isArray(pages)) {
        return
      }

      // 缓存页面
      state.assets.pages = pages.map(i => {
        return {
          id: i.id,
          parentId: i.parentId,
          title: i.title,
          description: i.description,
          icon: i.icon,
          level: i.level,
          path: i.url,
          seq: i.seq,
          anon: !i.requireAuthenticated && !i.requireAuthorized
        }
      })

      // 更新路由
      unreliablyUpdateRoutes(state.assets.pages)
    },

    logout (state) {
      state.credential.authenticated = false

      state.assets.user = null
      state.assets.permissions = null
      state.assets.pages = null
    },

    showLoading (state) {
      state.lock.loading++
    },

    hideLoading (state) {
      state.lock.loading--
    }
  },
  // 使用localStorage持久化
  plugins: [createPersistedState({
    // 持久化凭证
    paths: ['credential'],
    storage: window.localStorage
  })]
})
