<script setup>
import { defineProps, ref, watch } from 'vue'
import StatefulButton from '@/components/StatefulButton.vue'

const props = defineProps({
  buttons: {
    type: Array,
    default: () => []
  },
  closable: {
    type: Boolean,
    default: false
  },
  container: {
    type: Function,
    default: () => document.querySelector('#app > .ant-spin-nested-loading > .ant-spin-container')
  },
  destroyOnClose: {
    type: Boolean,
    default: true
  }
})

const open = ref(false)

const show = () => {
  open.value = true
}

const close = () => {
  open.value = false
}

defineExpose({
  show,
  close
})

const emits = defineEmits(['open', 'close'])

watch(
  () => open.value,
  value => {
    emits(value ? 'open' : 'close')
  })
</script>

<template>
  <a-modal
    v-bind="$attrs"
    v-model:open="open"
    :closable="props.closable"
    :get-container="props.container"
  >
    <template #footer>
      <a-space>
        <template
          v-for="(i, index) in props.buttons"
          :key="index"
        >
          <StatefulButton v-bind="i">
            {{ i.title }}
          </StatefulButton>
        </template>
      </a-space>
    </template>

    <template
      v-for="(index, name) in $slots"
      #[name]
    >
      <slot :name="name" />
    </template>
  </a-modal>
</template>
