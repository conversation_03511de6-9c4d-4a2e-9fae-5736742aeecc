<script setup>
import { onMounted, ref } from 'vue'
import * as sdk from 'microsoft-cognitiveservices-speech-sdk'

const props = defineProps({
  speechKey: {
    type: String,
    default: ''
  },
  serviceRegion: {
    type: String,
    default: ''
  }
})

const STATUS = {
  READY: 'ready',
  RECORDING: 'recording',
  CONVERTING: 'converting'
}

const status = ref(STATUS.READY)

const toggle = () => {
  switch (status.value) {
    case STATUS.READY:
      start()
      break
    case STATUS.RECORDING:
      stop()
      break
  }
}

const emit = defineEmits(['start', 'recognizing', 'completed'])

const start = () => {
  if (status.value !== STATUS.READY) {
    return
  }

  recognizer?.startContinuousRecognitionAsync()

  status.value = STATUS.RECORDING

  emit('start')
}

function stop () {
  status.value = STATUS.CONVERTING

  recognizer?.stopContinuousRecognitionAsyncImpl()
    .finally(() => {
      status.value = STATUS.READY

      emit('completed', text.filter(i => i !== '').join('/n'))
    })
}

let recognizer = null
const text = ['']

onMounted(() => {
  recognizer?.dispose()

  const _speechConfig = sdk.SpeechConfig.fromSubscription(props.speechKey, props.serviceRegion)
  _speechConfig.speechRecognitionLanguage = 'zh-CN'
  recognizer = new sdk.SpeechRecognizer(_speechConfig, sdk.AudioConfig.fromDefaultMicrophoneInput())

  recognizer.recognizing = (s, e) => {
    const _word = e.result.text.replace(text[text.length - 1], '')

    emit('recognizing', _word)

    text[text.length - 1] = e.result.text
  }

  recognizer.recognized = (s, e) => {
    text.push('')
  }

  recognizer.canceled = (s, e) => {
    stop()
  }
})
</script>

<template>
  <a-button
    :type="'default'"
    :shape="'circle'"
    :class="status !== STATUS.READY ? 'btn-running' : ''"
    @click="toggle"
  >
    <template #icon>
      <template v-if="status === STATUS.READY">
        <audio-outlined />
      </template>
      <template v-else>
        <svg
          viewBox="0 0 100 100"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>中止</title>
          <g transform="translate(50, 50)">
            <rect
              x="-24"
              y="-25"
              width="8"
              height="50"
              rx="4"
              opacity="0.6"
            >
              <animate
                attributeName="height"
                values="50;15;50"
                dur="0.8s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                values="-25;-7.5;-25"
                dur="0.8s"
                repeatCount="indefinite"
              />
            </rect>
            <rect
              x="-12"
              y="-25"
              width="8"
              height="50"
              rx="4"
              opacity="0.8"
            >
              <animate
                attributeName="height"
                values="15;50;15"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.2s"
              />
              <animate
                attributeName="y"
                values="-7.5;-25;-7.5"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.2s"
              />
            </rect>
            <rect
              x="0"
              y="-25"
              width="8"
              height="50"
              rx="4"
            >
              <animate
                attributeName="height"
                values="50;15;50"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.4s"
              />
              <animate
                attributeName="y"
                values="-25;-7.5;-25"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.4s"
              />
            </rect>
            <rect
              x="12"
              y="-25"
              width="8"
              height="50"
              rx="4"
              opacity="0.8"
            >
              <animate
                attributeName="height"
                values="15;50;15"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.6s"
              />
              <animate
                attributeName="y"
                values="-7.5;-25;-7.5"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.6s"
              />
            </rect>
            <rect
              x="24"
              y="-25"
              width="8"
              height="50"
              rx="4"
              opacity="0.6"
            >
              <animate
                attributeName="height"
                values="50;15;50"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.8s"
              />
              <animate
                attributeName="y"
                values="-25;-7.5;-25"
                dur="0.8s"
                repeatCount="indefinite"
                begin="0.8s"
              />
            </rect>
          </g>
        </svg>
      </template>
    </template>
  </a-button>
</template>

<style lang="less" scoped>
@import '@/less/default';

.btn-running {
  padding: 0 !important;
  overflow: hidden;

  svg {
    width: 30px;
    height: 30px;
    fill: @primary-color;
  }
}
</style>
