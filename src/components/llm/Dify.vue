<script setup>
import { computed, ref } from 'vue'
import PluginApi from '@/api/llm/plugin.js'

const props = defineProps({
  // 路由
  route: {
    type: String,
    default: ''
  }
})

const baseURL = import.meta.env.VITE_DIFY_BASE_URL

// 登录地址
const signInURL = computed(() => {
  return `${window.location.origin}${baseURL}/signin`
})

const route = computed(() => {
  return `${window.location.origin}${baseURL}${props.route}`
})

const dify = ref()

const emits = defineEmits(['load', 'route'])

const load = () => {
  const _window = dify.value.contentWindow
  const _document = _window.document

  // 注入样式
  const _style = _document.createElement('style')
  _style.textContent = `
    /* 隐藏顶部栏 */
    body > div > div > div.basis-auto {
      display: none;
    }
  `
  _document.head.appendChild(_style)

  // 注入路由监听脚本
  const _script = `
    (function() {
      const _pushState = window.history.pushState;
      const _replaceState = window.history.replaceState;

      window.history.pushState = function(...args) {
        _pushState.apply(window.history, args);
        window.parent.postMessage({
          type: 'dify-route-change',
          url: window.location.href
        }, "*");
      };

      window.history.replaceState = function(...args) {
        _replaceState.apply(window.history, args);
        window.parent.postMessage({
          type: "dify-route-change",
          url: window.location.href
        }, "*");
      };

      window.addEventListener("popstate", () => {
        window.parent.postMessage({
          type: "dify-route-change",
          url: window.location.href
        }, "*");
      });
    })();
  `
  _window.eval(_script)

  // 监听消息
  window.addEventListener('message', (event) => {
    if (event.data.type === 'dify-route-change') {
      // 重定向到登录页面
      if (signInURL.value === event.data.url) {
        PluginApi.difyToken({
          toast: {
            success: false
          }
        }).then(token => {
          // 缓存token
          _window.localStorage.setItem('console_token', token.data.accessToken)
          _window.localStorage.setItem('refresh_token', token.data.refreshToken)

          // 重定向到目标页面
          _window.location.href = route.value
        })
      } else {
        emits('route', event)
      }
    }
  })

  emits('load')
}

// 暴露方法给父组件
defineExpose({
  window: () => dify.value.contentWindow,
  document: () => dify.value.contentWindow.document
})
</script>

<template>
  <iframe
    ref="dify"
    :src="route"
    @load="load"
  />
</template>

<style scoped>
iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
