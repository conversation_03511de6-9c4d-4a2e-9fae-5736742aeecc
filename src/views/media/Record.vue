<script setup>
import { getCurrentInstance, nextTick, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import FeedbackUtil from '@/utils/feedback.js'
import RecordApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

const gutter = [8, 8]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'name',
    icon: 'FileTextOutlined',
    type: 'text'
  }, {
    title: '存储类型',
    field: 'temporary',
    type: 'select',
    config: {
      options: [{
        label: '临时文件',
        value: true
      }, {
        label: '正式文件',
        value: false
      }]
    }
  }],

  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const selectedKeys = ref([])
const directories = ref([{
  title: '根目录',
  key: ''
}])
const loadDirectories = node => {
  return new Promise(resolve => {
    RecordApi.subPath(node.key, {
      showLoading: false,
      toast: {
        success: false
      }
    })
      .then(result => {
        if (!Array.isArray(node.dataRef.children)) {
          node.dataRef.children = []
        }

        if (result.data.length === 0) {
          node.dataRef.isLeaf = true
        } else {
          result.data.forEach(i => {
            node.dataRef.children.push({
              title: i.right,
              key: i.left
            })
          })
        }

        directories.value = [...directories.value]
      })
      .finally(() => resolve())
  })
}
const selectDirectory = () => {
  table.value.load()
}

const table = ref()
const tableOptions = {
  columns: [{
    title: 'id',
    dataIndex: 'id'
  }, {
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '扩展名',
    dataIndex: 'extension'
  }, {
    title: '存储类型',
    dataIndex: 'isTemporary',
    type: 'select',
    config: {
      options: [{
        label: '临时文件',
        value: true
      }, {
        label: '正式文件',
        value: false
      }]
    },
    sorter: true
  }, {
    title: '内容',
    dataIndex: 'content'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }],
  actions: [{
    title: '删除',
    icon: 'DeleteOutlined',
    danger: true,
    callback (record) {
      const _promise = RecordApi.remove(record.id, true)

      _promise.then(() => {
        table.value.load()
      })

      return _promise
    }
  }, {
    title: '预览',
    icon: 'SearchOutlined',
    callback (record) {
      if (['avif', 'bmp', 'gif', 'ico', 'jfif', 'jpeg', 'jpg', 'jxl', 'pjp', 'pjpeg', 'png', 'svg', 'svgz', 'tif', 'tiff', 'webp', 'xbm'].indexOf(record.extension) !== -1) {
        imageViewer.value.src = RecordApi.preview(record.id)
        imageViewer.value.visible = true
      } else if (['asx', 'avi', 'm4v', 'mov', 'mp4', 'mpeg', 'mpg', 'ogm', 'ogv', 'webm', 'wmv'].indexOf(record.extension) !== -1) {
        video.value.src = RecordApi.play(record.id)
        videoPlayer.value.show()

        nextTick(() => {
          document.querySelector('video').onloadeddata = function () {
            video.value.width = this.videoWidth
          }
        })
      } else {
        FeedbackUtil.message('抱歉，该文件暂不支持预览', 'warn')
      }
    }
  }, {
    title: '下载',
    icon: 'DownloadOutlined',
    callback (record) {
      RecordApi.download(record.id)
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  console.log(_filters)

  return new Promise(resolve => {
    RecordApi.search(count, index, sorters, selectedKeys.value.length === 0 ? null : selectedKeys.value[0], _filters.name, _filters.temporary, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const toolbar = [{
  title: '小文件上传',
  icon: 'UploadOutlined',
  callback () {
    proxy.$refs.json.click()
  }
}, {
  title: '大文件上传',
  icon: 'UploadOutlined',
  callback () {
    proxy.$refs.multipart.click()
  }
}]

const uploadBody = event => {
  if (event.target.files.length === 0) {
    return
  }

  const _reader = new FileReader()
  _reader.readAsDataURL(event.target.files[0])
  _reader.onload = function (e) {
    RecordApi.uploadBody(selectedKeys.value.length === 0 ? null : selectedKeys.value[0], [{
      name: event.target.files[0].name,
      base64: this.result.split('base64,')[1]
    }], false).then(result => {
      if (result.data[0].success) {
        table.value.load()
      }
    })
  }
}

const uploadForm = event => {
  RecordApi.uploadForm(selectedKeys.value.length === 0 ? null : selectedKeys.value[0], [event.target.files[0]], false).then(result => {
    if (result.data[0].success) {
      table.value.load()
    }
  })
}

const imageViewer = ref({
  visible: false,
  onVisibleChange (value) {
    imageViewer.value.visible = value
  }
})

const videoPlayer = ref()

const video = ref({
  src: null,
  width: 520,
  onClose: () => {
    video.value.src = null
    video.value.width = 520
  }
})
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <a-row :gutter="gutter">
      <a-col
        :xs="24"
        :md="8"
        :xl="4"
      >
        <a-card
          :size="'small'"
          style="height: 100%"
        >
          <a-directory-tree
            v-model:selected-keys="selectedKeys"
            :load-data="loadDirectories"
            :tree-data="directories"
            @select="selectDirectory"
          />
        </a-card>
      </a-col>
      <a-col
        :xs="24"
        :md="16"
        :xl="20"
      >
        <a-card :size="'small'">
          <DataTable
            ref="table"
            :actions="tableOptions.actions"
            :columns="tableOptions.columns"
            :load="load"
            :toolbar="toolbar"
          />
        </a-card>
      </a-col>
    </a-row>

    <input
      ref="json"
      style="display: none"
      type="file"
      @change="uploadBody($event)"
    >

    <input
      ref="multipart"
      style="display: none"
      type="file"
      @change="uploadForm($event)"
    >
  </div>

  <!-- 图片预览 -->
  <a-image
    :src="imageViewer.src"
    :preview="imageViewer"
    :fallback="'data:image/png;base64,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'"
    style="display: none"
  />

  <StatefulModal
    ref="videoPlayer"
    :width="video.width"
    @close="video.onClose"
  >
    <video
      :src="video.src"
      controls="controls"
      style="max-width: 100%;"
    >
      您的浏览器不支持视频播放器
    </video>
  </StatefulModal>
</template>
