<script setup>
import { getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import RoleApi from '@/api/sec/role.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.domain.model.sec.Role', ['name', 'description'])

const fields = [{
  title: '名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = RoleApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = RoleApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

onMounted(() => {
  if (proxy.$route.query.id) {
    RoleApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>
</template>
