<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import DataTable from '@/components/DataTable.vue'
import OrganizationPicker from '@/components/OrganizationPicker.vue'
import SearchBar from '@/components/SearchBar.vue'
import FeedbackUtil from '@/utils/feedback.js'
import RoleApi from '@/api/sec/role.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '登录账号',
    field: 'account',
    icon: 'IdcardOutlined',
    type: 'text'
  }, {
    title: '名称',
    field: 'username',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: '手机号码',
    field: 'mp',
    icon: 'MobileOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const organizationPicker = ref()

const table = ref()
const tableOptions = {
  columns: [{
    title: '登录账号',
    dataIndex: 'account',
    sorter: true
  }, {
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '部门',
    dataIndex: 'deptFullName'
  }, {
    title: '手机号码',
    dataIndex: 'mp',
    sorter: true
  }, {
    title: '性别',
    dataIndex: 'isMale',
    type: 'select',
    config: {
      options: [{
        label: '男性',
        value: true
      }, {
        label: '女性',
        value: false
      }]
    }
  }],
  actions: [{
    title: '解除',
    icon: 'UserDeleteOutlined',
    danger: true,
    callback (record) {
      return new Promise(resolve => {
        FeedbackUtil.modal('即将解除该用户的任命，是否确认？', 'confirm', {
          icon: createVNode(ExclamationCircleOutlined),
          onOk () {
            RoleApi.removeUser(proxy.$route.query.id, record.id, {
              showLoading: false
            })
              .then(() => {
                table.value.load()
              })
              .finally(() => {
                resolve()
              })
          },
          onCancel () {
            resolve()
          }
        })
      })
    }
  }],
  toolbar: [{
    title: '任命',
    icon: 'UserAddOutlined',
    callback () {
      organizationPicker.value.show()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    RoleApi.findUser(count, index, sorters, proxy.$route.query.id, _filters.account, _filters.username, _filters.mp, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const addUser = (departments, users) => {
  if (users.length === 0) {
    return
  }

  RoleApi.addUser(proxy.$route.query.id, users[0].id).then(() => {
    table.value.load()
  })
}

const ready = ref(false)
const role = ref({
  name: null,
  description: null,
  creatorName: null,
  createTime: null
})
onMounted(() => {
  ready.value = true

  RoleApi.get(proxy.$route.query.id, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    role.value = result.data
  })
})
</script>

<script>
export default {
  beforeRouteEnter (to, from, next) {
    if (Object.prototype.hasOwnProperty.call(to, 'query') && Object.prototype.hasOwnProperty.call(to.query, 'id')) {
      next()
    } else {
      next({
        path: '/sec/role'
      })
    }
  }
}
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header
        :title="'人员任命'"
        :sub-title="role.name || '-'"
        @back="() => $router.go(-1)"
      >
        <a-divider />
        <a-descriptions
          :column="{ xs: 1, md: 3, xl: 6 }"
          :size="'small'"
        >
          <a-descriptions-item :label="'角色描述'">
            {{ role.description || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建人员'">
            {{ role.creatorName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建时间'">
            {{ role.createTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-page-header>
    </teleport>
  </template>

  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
      :toolbar="tableOptions.toolbar"
    />
  </div>

  <OrganizationPicker
    ref="organizationPicker"
    :max="1"
    :type="'user'"
    @picked="addUser"
  />
</template>
