<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import ApplicationApi from '@/api/llm/application.js'
import MediaApi from '@/api/media/record.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const records = ref([])

const baseURL = import.meta.env.VITE_DIFY_BASE_URL

const icon = icon => {
  return `${baseURL}${icon}`
}

const isPath = icon => {
  return typeof icon === 'string' && icon.startsWith('/')
}

const load = () => {
  ApplicationApi.searchUserApplications(null, null, {
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data
  })
}

const getType = record => {
  if (Array.isArray(record.datasets)) {
    return 'dataset'
  } else if (typeof record.link === 'string') {
    return 'link'
  } else if (typeof record.prompt === 'string') {
    return 'prompt'
  } else {
    return 'workflow'
  }
}

const enter = record => {
  const _type = getType(record)

  switch (_type) {
    case 'link':
      if (record.target === 'BLANK') {
        window.open(record.link)
      } else {
        proxy.$router.push({
          path: record.link,
          query: {
            id: record.id
          }
        })
      }

      break
    case 'workflow':
      ApplicationApi.installId(record.platformId, {
        toast: {
          success: false
        }
      }).then(result => {
        proxy.$router.push({
          name: 'llm.application.workflow',
          query: {
            id: result.data
          }
        })
      })

      break
    default:
      proxy.$router.push({
        name: 'llm.conversation',
        query: {
          applicationId: record.id
        }
      })

      break
  }
}

const edit = record => {
  const _type = getType(record)

  proxy.$router.push({
    name: `llm.application.${_type}.edit`,
    query: {
      id: _type === 'workflow' ? record.platformId : record.id
    }
  })
}

const types = [{
  type: 'dataset',
  title: '知识库对话',
  description: '基于知识库文档进行智能问答',
  icon: 'DatabaseOutlined',
  color: '#1890ff'
}, {
  type: 'prompt',
  title: '提示词',
  description: '基于提示词模板的对话应用',
  icon: 'MessageOutlined',
  color: '#fa8c16'
}, {
  type: 'workflow',
  title: '工作流',
  description: '创建复杂的AI工作流程',
  icon: 'ApartmentOutlined',
  color: '#52c41a'
}, {
  type: 'link',
  title: '外链',
  description: '集成外部应用或服务',
  icon: 'LinkOutlined',
  color: '#eb2f96'
}]

const getTypeConfig = record => {
  const _type = getType(record)
  return types.find(i => i.type === _type)
}

const showTypeOptions = ref(false)

const selectApplicationType = type => {
  showTypeOptions.value = false

  proxy.$router.push({
    name: `llm.application.${type}.edit`
  })
}

const remove = id => {
  FeedbackUtil.modal('您即将删除该记录，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = ApplicationApi.remove(id, {
        showLoading: false
      })
      _promise.then(() => {
        load()
      })

      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="container">
    <a-row :gutter="[16, 16]">
      <!-- 新建应用卡片 -->
      <a-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="create-card"
          :class="{ 'flipped': showTypeOptions }"
          :hoverable="!showTypeOptions"
        >
          <div class="card-inner">
            <!-- 初始状态：显示新建按钮 -->
            <transition name="flip-front" appear>
              <div v-if="!showTypeOptions" class="card-face card-front" @click="showTypeOptions = true">
                <div class="create-card-content">
                  <div class="create-icon-wrapper">
                    <plus-outlined class="create-icon" />
                  </div>
                  <h3 class="create-title">
                    新建应用
                  </h3>
                  <p class="create-description">
                    创建一个新的数智人应用
                  </p>
                </div>
              </div>
            </transition>

            <!-- 展开状态：显示4个应用类型选项 -->
            <transition name="flip-back" appear>
              <div v-if="showTypeOptions" class="card-face card-back">
                <div class="type-options-content">
                  <div class="type-options-header">
                    <h3 class="options-title">选择应用类型</h3>
                    <a-button
                      type="text"
                      size="small"
                      class="close-btn"
                      @click="showTypeOptions = false"
                    >
                      <close-outlined />
                    </a-button>
                  </div>

                  <div class="type-options-grid">
                    <div
                      v-for="(type, index) in types"
                      :key="type.type"
                      class="type-option-item"
                      :style="{ animationDelay: `${index * 0.1}s` }"
                      @click="selectApplicationType(type.type)"
                    >
                      <div
                        class="type-option-icon"
                        :style="{
                          backgroundColor: type.color + '15',
                          borderColor: type.color + '30'
                        }"
                      >
                        <component
                          :is="type.icon"
                          :style="{ color: type.color }"
                        />
                      </div>
                      <div class="type-option-text">
                        <div class="type-option-title">{{ type.title }}</div>
                        <div class="type-option-desc">{{ type.description }}</div>
                      </div>
                      <div class="type-option-arrow">
                        <right-outlined />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </a-card>
      </a-col>

      <!-- 应用卡片列表 -->
      <a-col
        v-for="i in records"
        :key="i.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="app-card"
          :hoverable="true"
        >
          <div class="app-card-header">
            <div class="app-avatar-wrapper">
              <!-- 工作流应用 -->
              <template v-if="i.platform === 'DIFY'">
                <template v-if="isPath(i.icon)">
                  <a-avatar
                    :src="icon(i.icon)"
                    :size="48"
                  />
                </template>
                <template v-else>
                  <a-avatar
                    :size="48"
                    class="app-avatar"
                  >
                    {{ i.icon }}
                  </a-avatar>
                </template>
              </template>
              <!-- 其它 -->
              <template v-else>
                <template v-if="typeof i.icon === 'string'">
                  <a-avatar
                    :src="MediaApi.preview(i.icon)"
                    :size="48"
                  />
                </template>
                <template v-else>
                  <a-avatar
                    :size="48"
                    class="app-avatar default-icon"
                    :style="{
                      backgroundColor: getTypeConfig(i).color + '20',
                      color: getTypeConfig(i).color
                    }"
                  >
                    <template #icon>
                      <component :is="getTypeConfig(i).icon" />
                    </template>
                  </a-avatar>
                </template>
              </template>
            </div>
          </div>

          <div class="app-card-content">
            <h3
              class="app-title"
              :title="i.name || '未命名'"
            >
              {{ i.name || '未命名' }}
            </h3>
            <p
              class="app-description"
              :title="i.description || '暂无描述'"
            >
              {{ i.description || '暂无描述' }}
            </p>
            <div class="app-meta">
              <a-space :size="8">
                <a-tag
                  :color="'blue'"
                  :size="'small'"
                >
                  <tag-outlined />
                  {{ getType(i) }}
                </a-tag>
              </a-space>
            </div>
          </div>

          <div class="app-card-actions">
            <a-tooltip title="对话">
              <a-button
                type="primary"
                shape="circle"
                size="small"
                class="action-btn primary-btn"
                @click="enter(i)"
              >
                <comment-outlined />
              </a-button>
            </a-tooltip>

            <!-- 创建人允许编辑 -->
            <template v-if="i.isOwner">
              <a-tooltip title="编辑">
                <a-button
                  type="default"
                  shape="circle"
                  size="small"
                  class="action-btn"
                  @click="edit(i)"
                >
                  <edit-outlined />
                </a-button>
              </a-tooltip>

              <a-tooltip title="删除">
                <a-button
                  type="default"
                  shape="circle"
                  size="small"
                  class="action-btn danger-btn"
                  @click="remove(i.id)"
                >
                  <delete-outlined />
                </a-button>
              </a-tooltip>
            </template>
          </div>
        </a-card>
      </a-col>
    </a-row>


  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.container {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

// 新建应用卡片
.create-card {
  height: 280px;
  border-radius: 16px;
  border: 2px solid #f0f0f0;
  background: #fff;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  perspective: 1000px;
  overflow: hidden;

  &:not(.flipped):hover {
    border-color: #1890ff;
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
  }

  &.flipped {
    border-color: #1890ff;
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 0;
    position: relative;
  }

  .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
  }

  .card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    box-sizing: border-box;
  }

  .card-front {
    transform: rotateY(0deg);
  }

  .card-back {
    transform: rotateY(180deg);
  }

  .create-card-content {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  .create-icon-wrapper {
    width: 72px;
    height: 72px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
  }

  .create-icon {
    font-size: 28px;
    color: #fff;
    transition: all 0.3s ease;
  }

  .create-title {
    font-size: 18px;
    font-weight: 700;
    color: #262626;
    margin: 0 0 12px 0;
    transition: all 0.3s ease;
  }

  .create-description {
    font-size: 14px;
    color: #8c8c8c;
    margin: 0;
    line-height: 1.5;
  }

  // 类型选项内容
  .type-options-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .type-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f0f0;
  }

  .options-title {
    font-size: 16px;
    font-weight: 700;
    color: #262626;
    margin: 0;
  }

  .close-btn {
    padding: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      color: #ff4d4f;
      background: rgba(255, 77, 79, 0.1);
      transform: rotate(90deg);
    }
  }

  .type-options-grid {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .type-option-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
    background: #fafafa;
    animation: slideInUp 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);

    &:hover {
      background: #fff;
      border-color: #1890ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

      .type-option-arrow {
        opacity: 1;
        transform: translateX(4px);
      }
    }
  }

  .type-option-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 18px;
    border: 1px solid;
    transition: all 0.3s ease;
  }

  .type-option-text {
    flex: 1;
  }

  .type-option-title {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
  }

  .type-option-desc {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
  }

  .type-option-arrow {
    opacity: 0;
    color: #1890ff;
    font-size: 12px;
    transition: all 0.3s ease;
    transform: translateX(0);
  }
}

// 应用卡片
.app-card {
  height: 280px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

    .app-card-actions {
      opacity: 1;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }

  .app-card-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .app-avatar {
    background: rgb(255, 234, 213);
    color: #fff;
    font-size: 20px;

    &.default-icon {
      background: transparent;
      font-size: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .app-card-content {
    flex: 1;
    text-align: center;
    margin-bottom: 20px;
  }

  .app-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .app-description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    min-height: 36px;
  }

  .app-meta {
    margin-bottom: 8px;

    :deep(.ant-tag) {
      margin: 0;
      border-radius: 8px;
      font-size: 12px;
      padding: 2px 8px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
  }

  .app-card-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.primary-btn {
      background: #1890ff;
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.danger-btn:hover {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .app-card,
  .create-card {
    height: 240px;
  }

  .create-card {
    .create-icon-wrapper {
      width: 50px;
      height: 50px;
      margin-bottom: 12px;
    }

    .create-icon {
      font-size: 20px;
    }

    .create-title {
      font-size: 14px;
    }
  }

  .app-card {
    .app-avatar {
      width: 40px !important;
      height: 40px !important;
      font-size: 16px;
    }

    .app-title {
      font-size: 14px;
    }

    .app-description {
      font-size: 12px;
    }
  }
}

// 翻转动画
.flip-front-enter-active,
.flip-front-leave-active,
.flip-back-enter-active,
.flip-back-leave-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.flip-front-enter-from {
  opacity: 0;
  transform: rotateY(-90deg) scale(0.8);
}

.flip-front-leave-to {
  opacity: 0;
  transform: rotateY(90deg) scale(0.8);
}

.flip-back-enter-from {
  opacity: 0;
  transform: rotateY(-90deg) scale(0.8);
}

.flip-back-leave-to {
  opacity: 0;
  transform: rotateY(90deg) scale(0.8);
}

// 滑入动画
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式动画延迟
.type-option-item:nth-child(1) { animation-delay: 0.1s; }
.type-option-item:nth-child(2) { animation-delay: 0.2s; }
.type-option-item:nth-child(3) { animation-delay: 0.3s; }
.type-option-item:nth-child(4) { animation-delay: 0.4s; }

// 悬浮效果增强
.create-card:not(.flipped) .create-card-content:hover {
  .create-icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .create-title {
    color: #1890ff;
  }
}

</style>
