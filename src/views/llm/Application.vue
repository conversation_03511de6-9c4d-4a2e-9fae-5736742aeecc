<script setup>
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import Dify from '@/components/llm/Dify.vue'

const { proxy } = getCurrentInstance()

const applicationId = ref('')

const route = computed(() => {
  return `/explore/installed/${applicationId.value}`
})

const dify = ref()

const load = () => {
  const _document = dify.value.document()

  // 注入样式
  const _style = _document.createElement('style')
  _style.textContent = `
    /* 隐藏侧边栏 */
    body > div > div > div.bg-background-body > div.border-divider-burn {
      display: none;
    }

    /* 隐藏对话列表 */
    body > div > div > div.bg-background-body > div.grow > div > div > div.ease-in-out > div > div.grow > div {
      display: none;
    }
  `
  _document.head.appendChild(_style)
}

onMounted(() => {
  applicationId.value = proxy.$route.query.id
  if (applicationId.value == null) {
    proxy.$router.replace({
      name: 'llm.application.index'
    })
  }
})
</script>

<template>
  <div
    v-if="applicationId"
    class="dify"
  >
    <Dify
      ref="dify"
      :route="route"
      @load="load"
    />
  </div>
</template>

<style scoped>
.dify {
  height: calc(100vh - 64px - 64px - 12px);
  width: 100%;
}
</style>
