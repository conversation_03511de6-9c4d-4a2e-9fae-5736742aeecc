<script setup>
import { getCurrentInstance, inject, onMounted, ref, createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import GenericFrom from '@/components/GenericForm.vue'
import DatasetApi from '@/api/llm/dataset.js'
import MediaApi from '@/api/media/record.js'
import UtilApi from '@/api/util.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Dataset', ['title', 'description'])

const fields = [{
  title: '知识库名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DatasetApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        // 重定向到编辑页面
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DatasetApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

const keyword = ref(null)
const loading = ref(false)

const columns = ref([{
  title: '名称',
  dataIndex: 'documentName'
}, {
  title: '创建人员',
  dataIndex: 'creatorName'
}, {
  title: '创建时间',
  dataIndex: 'createTime'
}, {
  title: '',
  key: 'action'
}])

const pagination = ref({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '50', '100'],
  responsive: true,
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total, range) => `共${total}项记录，当前为第${range[0]}-${range[1]}项`,
  total: 0
})

const documents = ref([])

const change = (pa, filters, sorters) => {
  pagination.value.pageSize = pa.pageSize

  loadDocuments(pa === null ? null : pa.current - 1)
}

// 加载文档列表
const loadDocuments = index => {
  if (index == null) {
    pagination.value.current = 1
    index = 0
  }

  loading.value = true

  DatasetApi.searchDocuments(pagination.value.pageSize, index, proxy.$route.query.id, keyword.value, {
    showLoading: false,
    toast: {
      success: false
    }
  })
    .then(result => {
      documents.value = result.data.records || []
      documents.value.forEach(i => {
        if (i.document !== null) {
          i.documentName = i.document.name
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}

const accept = ref('*')

const uploadDocument = ({ action, data, file, filename, headers, onError, onProgress, onSuccess, withCredentials }) => {
  DatasetApi.uploadDocument(null, proxy.$route.query.id, null, [file], {
    toast: {
      success: true
    }
  }).then(result => {
    if (result.data[0].success) {
      loadDocuments()
    }
  })
}

// 下载文档
const downloadDocument = document => {
  MediaApi.download(document.id)
}

// 删除文档
const deleteDocument = documentId => {
  FeedbackUtil.modal('您即将删除该文档，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = DatasetApi.removeDocument(documentId, {
        showLoading: false
      })
      _promise.then(() => {
        loadDocuments()
      })
      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  if (proxy.$route.query.id) {
    DatasetApi.get(proxy.$route.query.id, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })

    loadDocuments()
  } else {
    form.value.setModel({
      // 设置默认值
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }

  DatasetApi.accept({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    accept.value = result.data
  })
})

</script>

<template>
  <div class="layout-content-panel">
    <template v-if="proxy.$route.query.id">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card :size="'small'">
            <GenericFrom
              ref="form"
              :layout="{
                showLabel: false
              }"
              :fields="fields"
              :actions="actions"
            />
          </a-card>
        </a-col>

        <a-col :span="18">
          <a-card
            :title="'文档管理'"
            :size="'small'"
          >
            <a-space
              :direction="'vertical'"
              :size="'large'"
              style="width: 100%"
            >
              <a-space style="width: 100%; justify-content: space-between;">
                <a-input-search
                  v-model:value="keyword"
                  :loading="loading"
                  :placeholder="'文档名称'"
                  @search="loadDocuments"
                />

                <a-upload
                  :accept="accept"
                  :custom-request="uploadDocument"
                  :max-count="1"
                  :show-upload-list="false"
                >
                  <a-button>
                    <upload-outlined />
                    上传
                  </a-button>
                </a-upload>
              </a-space>

              <a-table
                :columns="columns"
                :data-source="documents"
                :loading="loading"
                :pagination="pagination"
                @change="change"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <span>
                      <a @click="downloadDocument(record)">下载</a>
                      <a-divider type="vertical" />
                      <a
                        style="color: #ff4d4f;"
                        @click="deleteDocument(record.id)"
                      >
                        删除
                      </a>
                    </span>
                  </template>
                  <template v-else>
                    {{ record[column.dataIndex] }}
                  </template>
                </template>

                <template #buildOptionText="props">
                  <span>{{ props.value }}条/页</span>
                </template>
              </a-table>
            </a-space>
          </a-card>
        </a-col>
      </a-row>
    </template>
    <template v-else>
      <GenericFrom
        ref="form"
        :fields="fields"
        :actions="actions"
      />
    </template>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.layout-content-panel {
  padding: @padding-sm;
}

// 响应式优化
@media (max-width: 768px) {
  .layout-content-panel {
    :deep(.ant-row) {
      flex-direction: column;

      .ant-col {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
