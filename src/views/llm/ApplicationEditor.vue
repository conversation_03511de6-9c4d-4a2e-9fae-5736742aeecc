<script setup>
import { computed, getCurrentInstance, ref } from 'vue'
import Dify from '@/components/llm/Dify.vue'
import ApplicationApi from '@/api/llm/application.js'

const { proxy } = getCurrentInstance()

const applicationId = computed(() => proxy.$route.query.id)

const route = computed(() => proxy.$route.query.id
  ? `/app/${proxy.$route.query.id}/configuration/`
  : '/apps')

const dify = ref()

const load = () => {
  const _document = dify.value.document()

  // 注入样式
  const _style = _document.createElement('style')
  _style.textContent = `
    div.content-start, div.h-screen.w-screen {
      padding: 0;
    }

    /* 隐藏滚动条 */
    div.overflow-y-auto {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    div.overflow-y-auto::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
    }

    /* 隐藏搜索栏 */
    body > div > div > div.bg-background-body > div.bg-background-body {
      display: none;
    }

    /* 隐藏应用卡片 */
    body > div > div > div.bg-background-body > div.content-start > div.group {
      display: none;
    }

    /* 隐藏复制按钮 */
    body > div.h-full > div > div > div.bg-background-default-subtle > div > div > div > div.bg-app-detail-bg > div.flex.shrink-0.flex-col.items-start.justify-center.gap-3.self-stretch.p-4 > div.self-stretch > button:not(:first-child) {
      display: none;
    }

    /* 隐藏删除按钮 */
    body > div > div > div > div.bg-background-default-subtle > div > div > div > div.bg-app-detail-bg > div.border-divider-subtle {
      display: none;
    }

    footer {
      display: none;
    }
  `
  _document.head.appendChild(_style)
}

const routed = event => {
  const _array = event.data.url.match(/app\/([0-9a-z\\-]{36})\/(workflow|configuration)/)
  if (Array.isArray(_array) && _array.length > 0 && !applicationId.value) {
    ApplicationApi.save({
      platform: 'DIFY',
      platformId: _array[1]
    }, {
      toast: {
        success: true
      }
    })
  }
}
</script>

<template>
  <div class="dify">
    <Dify
      ref="dify"
      :route="route"
      @load="load"
      @route="routed"
    />
  </div>
</template>

<style lang="less" scoped>
.dify {
  height: calc(100vh - 64px - 64px - 12px);
  width: 100%;
}
</style>
