<script setup>
import { getCurrentInstance, inject, onMounted, ref } from 'vue'
import Dify from '@/components/llm/Dify.vue'
import GenericFrom from '@/components/GenericForm.vue'
import UtilApi from '@/api/util.js'
import DatasetApi from '@/api/llm/dataset.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Dataset', ['title', 'description'])

const fields = [{
  title: '知识库名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DatasetApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        DatasetApi.get(result.data, {
          toast: {
            success: false
          }
        }).then(result => {
          // 重定向到编辑页面
          proxy.$router.replace({
            query: {
              id: result.data.difyId
            }
          })
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DatasetApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const dify = ref()

const load = () => {
  const _document = dify.value.document()

  // 注入样式
  const _style = _document.createElement('style')
  _style.textContent = `
    /* 隐藏滚动条 */
    div.overflow-y-auto {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    div.overflow-y-auto::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
    }
  `
  _document.head.appendChild(_style)
}

const routed = event => {
  const _array = event.data.url.match(/datasets\/([0-9a-z\\-]{36})\/settings/)
  if (Array.isArray(_array) && _array.length > 0) {
    const _document = dify.value.document()

    // 注入样式
    const _style = _document.createElement('style')
    _style.textContent = `
      .w-full > .flex:nth-child(1), .w-full > .flex:nth-child(2), .w-full > .flex:nth-child(3) {
        display: none;
      }
    `
    _document.head.appendChild(_style)
  }
}

onMounted(() => {
  if (!proxy.$route.query.id) {
    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})
</script>

<template>
  <template v-if="proxy.$route.query.id">
    <div class="dify">
      <Dify
        ref="dify"
        :route="`/datasets/${proxy.$route.query.id}/documents`"
        @load="load"
        @route="routed"
      />
    </div>
  </template>
  <template v-else>
    <div class="layout-content-panel">
      <GenericFrom
        :fields="fields"
        :actions="actions"
      />
    </div>
  </template>
</template>

<style lang="less" scoped>
.dify {
  height: calc(100vh - 64px - 64px - 12px);
  width: 100%;
}
</style>
