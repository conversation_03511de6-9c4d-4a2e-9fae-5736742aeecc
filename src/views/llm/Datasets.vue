<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import GenericIcon from '@/components/GenericIcon.vue'
import DatasetApi from '@/api/llm/dataset.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const records = ref([])

const load = () => {
  DatasetApi.me({
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data
  })
}

const edit = record => {
  // 跳转到知识库编辑页面
  proxy.$router.push({
    name: 'llm.dataset.edit',
    query: record === null
      ? {}
      : {
          // id: record.id
          id: record.difyId
        }
  })
}

const remove = id => {
  FeedbackUtil.modal('您即将删除该知识库，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = DatasetApi.remove(id, {
        showLoading: false
      })
      _promise.then(() => {
        load()
      })

      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="container">
    <a-row :gutter="[16, 16]">
      <!-- 新建知识库卡片 -->
      <a-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="create-card"
          :hoverable="true"
          @click="edit(null)"
        >
          <div class="create-card-content">
            <div class="create-icon-wrapper">
              <plus-outlined class="create-icon" />
            </div>
            <h3 class="create-title">
              新建
            </h3>
            <p class="create-description">
              创建一个新的知识库
            </p>
          </div>
        </a-card>
      </a-col>

      <!-- 知识库卡片列表 -->
      <a-col
        v-for="i in records"
        :key="i.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="dataset-card"
          :hoverable="true"
        >
          <div class="dataset-card-header">
            <div class="dataset-avatar-wrapper">
              <a-avatar
                :size="48"
                class="dataset-avatar"
              >
                <template #icon>
                  <GenericIcon :icon="'icon-shujuku'" />
                </template>
              </a-avatar>
            </div>
          </div>

          <div class="dataset-card-content">
            <h3
              class="dataset-title"
              :title="i.title || '未命名'"
            >
              {{ i.title || '未命名' }}
            </h3>
            <p
              class="dataset-description"
              :title="i.description || '暂无描述'"
            >
              {{ i.description || '暂无描述' }}
            </p>
            <div class="dataset-meta">
              <a-space :size="8">
                <a-tag
                  :color="'blue'"
                  :size="'small'"
                >
                  <file-text-outlined />
                  {{ i.documentCount || 0 }} 个文档
                </a-tag>
              </a-space>
            </div>
          </div>

          <div class="dataset-card-actions">
            <a-tooltip title="管理文档">
              <a-button
                type="primary"
                shape="circle"
                size="small"
                class="action-btn primary-btn"
                @click="edit(i)"
              >
                <folder-open-outlined />
              </a-button>
            </a-tooltip>

            <a-tooltip title="删除">
              <a-button
                type="default"
                shape="circle"
                size="small"
                class="action-btn danger-btn"
                @click="remove(i.id)"
              >
                <delete-outlined />
              </a-button>
            </a-tooltip>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.container {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

// 新建知识库卡片
.create-card {
  height: 280px;
  border-radius: 12px;
  border: 2px dashed #d9d9d9;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

    .create-icon {
      color: #1890ff;
    }

    .create-title {
      color: #1890ff;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .create-card-content {
    text-align: center;
  }

  .create-icon-wrapper {
    width: 60px;
    height: 60px;
    margin: 0 auto 16px;
    border-radius: 50%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .create-icon {
    font-size: 24px;
    color: #8c8c8c;
    transition: color 0.3s ease;
  }

  .create-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
  }

  .create-description {
    font-size: 14px;
    color: #8c8c8c;
    margin: 0;
  }
}

// 知识库卡片
.dataset-card {
  height: 280px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

    .dataset-card-actions {
      opacity: 1;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }

  .dataset-card-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .dataset-avatar {
    background: #1890ff;
    color: #fff;
    font-size: 20px;
  }

  .dataset-card-content {
    flex: 1;
    text-align: center;
    margin-bottom: 20px;
  }

  .dataset-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dataset-description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 36px;
  }

  .dataset-meta {
    margin-bottom: 8px;

    :deep(.ant-tag) {
      margin: 0;
      border-radius: 8px;
      font-size: 12px;
      padding: 2px 8px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
  }

  .dataset-card-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.primary-btn {
      background: #1890ff;
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.danger-btn:hover {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .dataset-card,
  .create-card {
    height: 240px;
  }

  .create-icon-wrapper {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .create-icon {
    font-size: 20px;
  }

  .create-title {
    font-size: 14px;
  }

  .dataset-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px;
  }

  .dataset-title {
    font-size: 14px;
  }

  .dataset-description {
    font-size: 12px;
  }
}
</style>
