<script setup>
import { getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import ModelApi from '@/api/llm/model.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Model', ['title', 'name', 'provider', 'apiType', 'baseURL', 'apiKey', 'mode'])

const fields = [{
  title: '名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '模型',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '供应商',
  field: 'provider',
  type: 'text',
  config: {
    promise: _config.provider
  }
}, {
  title: 'API协议',
  field: 'apiType',
  type: 'select',
  config: {
    promise: new Promise(resolve => {
      _config.apiType.then(data => {
        data.options = [{
          label: 'OpenAI',
          value: 'OPEN_AI'
        }, {
          label: 'Azure OpenAI',
          value: 'AZURE_OPEN_AI'
        }, {
          label: '通义千问',
          value: 'QWEN'
        }, {
          label: '深度求索',
          value: 'DEEP_SEEK'
        }]

        resolve(data)
      })
    })
  }
}, {
  title: 'BaseURL',
  field: 'baseURL',
  type: 'text',
  config: {
    promise: _config.baseURL
  }
}, {
  title: 'ApiKey',
  field: 'apiKey',
  type: 'text',
  config: {
    promise: _config.apiKey
  }
}, {
  title: '类型',
  field: 'mode',
  type: 'select',
  config: {
    promise: new Promise(resolve => {
      _config.mode.then(data => {
        data.options = [{
          label: 'EMBEDDING',
          value: 'EMBEDDING'
        }, {
          label: '大模型',
          value: 'DEFAULT'
        }, {
          label: '推理模型',
          value: 'REASONING'
        }]

        resolve(data)
      })
    })
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')

const actions = ref([{
  callback (record) {
    const _promise = ModelApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = ModelApi.remove(record.id)

    _promise.then(() => {
      proxy.$router.replace({
        name: 'admin.sys.llm.model.index'
      })
    })

    return _promise
  }
}])

const form = ref()

onMounted(() => {
  if (proxy.$route.query.id) {
    ModelApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      isEnabled: true
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>
</template>
