<script setup>
import { getCurrentInstance, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import ApplicationApi from '@/api/llm/application.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'name',
    icon: 'TagOutlined',
    type: 'text'
  }, {
    title: '类型',
    field: 'type',
    type: 'select',
    config: {
      options: [{
        label: '知识库',
        value: 'dataset'
      }, {
        label: '提示词',
        value: 'prompt'
      }, {
        label: '工作流',
        value: 'workflow'
      }, {
        label: '外链',
        value: 'link'
      }]
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '描述',
    dataIndex: 'description'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }],
  actions: [{
    title: '授权',
    icon: 'SafetyOutlined',
    callback (record) {
      proxy.$router.push({
        name: 'admin.sys.llm.application.authorization',
        query: {
          id: record.id
        }
      })

      return Promise.resolve()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    ApplicationApi.search(count, index, _filters.type, _filters.name, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :mapper="tableOptions.mapper"
      :show-filter="false"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
