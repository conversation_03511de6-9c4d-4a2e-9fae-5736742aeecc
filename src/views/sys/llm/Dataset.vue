<script setup>
import { getCurrentInstance, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import DatasetApi from '@/api/llm/dataset.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'title',
    icon: 'TagOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: '名称',
    dataIndex: 'title',
    sorter: true
  }, {
    title: '描述',
    dataIndex: 'description'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }],
  actions: [{
    title: '授权',
    icon: 'SafetyOutlined',
    callback (record) {
      proxy.$router.push({
        name: 'admin.sys.llm.dataset.authorization',
        query: {
          id: record.id
        }
      })

      return Promise.resolve()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    DatasetApi.search(count, index, _filters.title, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :mapper="tableOptions.mapper"
      :show-filter="false"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
