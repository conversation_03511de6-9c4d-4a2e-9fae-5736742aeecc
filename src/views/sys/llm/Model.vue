<script setup>
import { ref } from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import ModelApi from '@/api/llm/model.js'

const _apiOptions = [{
  label: 'OpenAI',
  value: 'OPEN_AI'
}, {
  label: 'Azure OpenAI',
  value: 'AZURE_OPEN_AI'
}, {
  label: '通义千问',
  value: 'QWEN'
}, {
  label: '深度求索',
  value: 'DEEP_SEEK'
}]

const _modeOptions = [{
  label: 'EMBEDDING',
  value: 'EMBEDDING'
}, {
  label: '大模型',
  value: 'DEFAULT'
}, {
  label: '推理模型',
  value: 'REASONING'
}]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'title',
    icon: 'TagOutlined',
    type: 'text'
  }, {
    title: '类型',
    field: 'mode',
    type: 'select',
    config: {
      options: _modeOptions
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/sys/llm/model',
    idField: 'id'
  },
  columns: [{
    title: '名称',
    dataIndex: 'title',
    sorter: true
  }, {
    title: '模型',
    dataIndex: 'name'
  }, {
    title: '供应商',
    dataIndex: 'provider'
  }, {
    title: 'API协议',
    dataIndex: 'apiType',
    type: 'select',
    config: {
      options: _apiOptions
    },
    sorter: true
  }, {
    title: 'BaseURL',
    dataIndex: 'baseURL',
    sorter: true
  }, {
    title: 'ApiKey',
    dataIndex: 'apiKey'
  }, {
    title: '类型',
    dataIndex: 'mode',
    type: 'select',
    config: {
      options: _modeOptions
    },
    sorter: true
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    ModelApi.search(count, index, sorters, _filters.title, _filters.mode, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const remove = record => {
  return ModelApi.remove(record.id)
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <EdiTable
      ref="table"
      :mapper="tableOptions.mapper"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :addable="true"
      :load="load"
      :remove="remove"
    />
  </div>
</template>
