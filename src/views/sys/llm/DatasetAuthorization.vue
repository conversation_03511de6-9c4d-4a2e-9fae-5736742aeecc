<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import DataTable from '@/components/DataTable.vue'
import OrganizationPicker from '@/components/OrganizationPicker.vue'
import SearchBar from '@/components/SearchBar.vue'
import FeedbackUtil from '@/utils/feedback.js'
import DatasetApi from '@/api/llm/dataset.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'name',
    icon: 'FolderOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const organizationPicker = ref()

const table = ref()
const tableOptions = {
  columns: [{
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '类型',
    dataIndex: 'type',
    type: 'select',
    config: {
      options: [{
        label: '部门',
        value: 'DEPARTMENT'
      }, {
        label: '个人',
        value: 'USER'
      }]
    }
  }],
  actions: [{
    title: '解除',
    icon: 'DeleteOutlined',
    danger: true,
    callback (record) {
      return new Promise(resolve => {
        FeedbackUtil.modal('即将解除该对象的权限，是否确认？', 'confirm', {
          icon: createVNode(ExclamationCircleOutlined),
          onOk () {
            DatasetApi.removeScope(record.id, {
              showLoading: false
            })
              .then(() => {
                table.value.load()
              })
              .finally(() => {
                resolve()
              })
          },
          onCancel () {
            resolve()
          }
        })
      })
    }
  }],
  toolbar: [{
    title: '授权',
    icon: 'PlusOutlined',
    callback () {
      organizationPicker.value.show()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    DatasetApi.searchScopes(count, index, proxy.$route.query.id, _filters.name, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const addScopes = (departments, users) => {
  const _promises = []

  departments.forEach(i => {
    _promises.push(DatasetApi.addScope(proxy.$route.query.id, 'DEPARTMENT', i.id))
  })

  users.forEach(i => {
    _promises.push(DatasetApi.addScope(proxy.$route.query.id, 'USER', i.id))
  })

  Promise.all(_promises).then(() => {
    table.value.load()
  })
}

const ready = ref(false)
const dataset = ref({
  name: null,
  description: null,
  creatorName: null,
  createTime: null
})
onMounted(() => {
  ready.value = true

  DatasetApi.get(proxy.$route.query.id, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    dataset.value = result.data
  })
})
</script>

<script>
export default {
  beforeRouteEnter (to, from, next) {
    if (Object.prototype.hasOwnProperty.call(to, 'query') && Object.prototype.hasOwnProperty.call(to.query, 'id')) {
      next()
    } else {
      next({
        path: '/sys/llm/dataset'
      })
    }
  }
}
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header
        :title="'知识库授权'"
        :sub-title="dataset.name || '-'"
        @back="() => $router.go(-1)"
      >
        <a-divider />
        <a-descriptions
          :column="{ xs: 1, md: 3, xl: 6 }"
          :size="'small'"
        >
          <a-descriptions-item :label="'知识库描述'">
            {{ dataset.description || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建人员'">
            {{ dataset.creatorName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建时间'">
            {{ dataset.createTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-page-header>
    </teleport>
  </template>

  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
      :toolbar="tableOptions.toolbar"
    />
  </div>

  <OrganizationPicker
    ref="organizationPicker"
    @picked="addScopes"
  />
</template>
