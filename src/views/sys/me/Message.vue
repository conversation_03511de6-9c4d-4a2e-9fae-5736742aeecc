<script setup>
import { ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import MessageApi from '@/api/sys/message.js'

const _statusOptions = [{
  label: '已读',
  value: true
}, {
  label: '未读',
  value: false
}]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '状态',
    field: 'alreadyRead',
    type: 'select',
    config: {
      options: _statusOptions
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: '类型',
    dataIndex: 'type',
    sorter: true
  }, {
    title: '标题',
    dataIndex: 'title',
    sorter: true
  }, {
    title: '详情',
    dataIndex: 'description'
  }, {
    title: '状态',
    dataIndex: 'alreadyRead',
    type: 'select',
    config: {
      options: _statusOptions
    },
    sorter: true
  }, {
    title: '发件人',
    dataIndex: 'creatorName'
  }, {
    title: '发送时间',
    dataIndex: 'createTime',
    sorter: true
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    MessageApi.search(count, index, sorters, _filters.alreadyRead, {
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
