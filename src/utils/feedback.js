import { message, Modal, notification } from 'ant-design-vue'
import jquery from 'jquery'
import { router } from '@/router/index.js'
import Store from '@/store/index.js'

class FeedbackUtil {
  constructor () {
    this.redirecting = false
  }

  // 加载中
  loadingBy (promise) {
    const that = this

    that.showLoading()

    promise.finally(() => {
      that.hideLoading()
    })
  }

  // 开启加载
  showLoading () {
    Store.commit('showLoading')
  }

  // 结束加载
  hideLoading () {
    Store.commit('hideLoading')
  }

  confirmToRedirectToLogin (message) {
    const that = this

    // 同时间仅支持一次确认
    if (that.redirecting) {
      return
    }

    that.redirecting = true

    that.modal(message, 'confirm', {
      onOk () {
        that.redirectToLogin()
      },
      onCancel () {
        that.redirecting = false
      }
    })
  }

  redirectToLogin () {
    const that = this

    // 重定向到登录页
    router.push({
      name: 'login.index'
    }).finally(() => {
      that.redirecting = false
    })
  }

  // 全局提示
  message (content, type, options) {
    const _options = {
      content
    }
    jquery.extend(true, _options, options)

    switch (type) {
      case 'error':
        return message.error(_options)
      case 'success':
        return message.success(_options)
      case 'warn':
        return message.warning(_options)
      default:
        return message.info(_options)
    }
  }

  // 通知
  notification (content, type, options) {
    const _options = {
      message: content,
      placement: 'topRight'
    }
    jquery.extend(true, _options, options)

    switch (type) {
      case 'error':
        notification.error(_options)
        break
      case 'success':
        notification.success(_options)
        break
      case 'warn':
        notification.warning(_options)
        break
      default:
        notification.info(_options)
        break
    }
  }

  modal (content, type, options) {
    const _options = {
      title: content,
      centered: true,
      mask: true,
      maskClosable: false,
      keyboard: true,
      okText: '确定',
      cancelText: '取消'
    }
    jquery.extend(true, _options, options)

    switch (type) {
      case 'confirm':
        return Modal.confirm(_options)
      case 'error':
        return Modal.error(_options)
      case 'success':
        return Modal.success(_options)
      case 'warn':
        return Modal.warning(_options)
      default:
        return Modal.info(_options)
    }
  }
}

export default new FeedbackUtil()
